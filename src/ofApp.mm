#include "ofApp.h"
#include "fontstash.h"

//--------------------------------------------------------------
void ofApp::setup(){
    // 初始化变量
    gpsEnabled = false;
    latitude = 0.0;
    longitude = 0.0;
    altitude = 0.0;
    accuracy = 0.0;
    locationStatus = "正在获取位置信息...";

    // 初始化路径绘制相关变量
    enablePathDrawing = true;
    pathLineWidth = 3.0f;
    pathColor = ofColor(0, 255, 100, 200);  // 绿色半透明
    minDistanceThreshold = 2.0f;  // 2米最小距离阈值
    mapScale = 100000.0f;  // 初始缩放比例
    hasInitialPosition = false;
    lastGpsPoint = ofVec2f(0, 0);
    mapCenter = ofVec2f(0, 0);
    touchStartTime = 0;

    // 设置背景色
    ofBackground(30, 30, 30);

    // 初始化字体
    // 使用ofxFontStash3加载微软雅黑粗体字体文件，支持中文显示
    try {
        chineseFont.load("fonts/msyhbd.ttf", 24);
        ofLogNotice("Font") << "微软雅黑粗体字体加载成功";
    } catch (const std::exception& e) {
        ofLogError("Font") << "字体加载失败: " << e.what();
        ofLogError("Font") << "请确保fonts/msyhbd.ttf文件存在于bin/data目录中";
    }

    // 初始化GPS
    // 注册GPS位置数据事件监听器
    ofAddListener(ofxGPS::newLocationDataEvent, this, &ofApp::onLocationData);

    // 启动GPS定位服务
    if(ofxGPS::startLocation()) {
        gpsEnabled = true;
        locationStatus = "GPS已启动，正在定位...";
        ofLogNotice("GPS") << "GPS定位服务已启动";
    } else {
        gpsEnabled = false;
        locationStatus = "GPS启动失败，请检查权限设置";
        ofLogError("GPS") << "GPS定位服务启动失败";
    }
}

//--------------------------------------------------------------
void ofApp::update(){
    // GPS数据通过事件系统自动更新，这里不需要手动检查
    // 位置数据会在onLocationData事件处理函数中更新
}

//--------------------------------------------------------------
void ofApp::draw(){
    // 绘制GPS路径
    if(enablePathDrawing) {
        drawGpsPath();
    }

    // 绘制位置信息
    drawLocationInfo();
}

//--------------------------------------------------------------
void ofApp::exit(){
    if(gpsEnabled) {
        // 停止GPS定位服务
        ofxGPS::stopLocation();
        // 移除事件监听器
        ofRemoveListener(ofxGPS::newLocationDataEvent, this, &ofApp::onLocationData);
    }
}

//--------------------------------------------------------------
void ofApp::drawLocationInfo(){
    float x = 50;
    float y = 100;
    float lineHeight = 50;

    // 标题
    ofSetColor(100, 200, 255);
    chineseFont.drawString("实时位置信息", x, y);

    y += lineHeight * 1.5;
    ofSetColor(255, 255, 255);

    // 状态信息
    chineseFont.drawString("状态: " + locationStatus, x, y);
    y += lineHeight;

    if(gpsEnabled && latitude != 0.0 && longitude != 0.0) {
        // 纬度
        string latStr = "纬度: " + ofToString(latitude, 6) + "°";
        chineseFont.drawString(latStr, x, y);
        y += lineHeight;

        // 经度
        string lonStr = "经度: " + ofToString(longitude, 6) + "°";
        chineseFont.drawString(lonStr, x, y);
        y += lineHeight;

        // 海拔
        string altStr = "海拔: " + ofToString(altitude, 2) + " 米";
        chineseFont.drawString(altStr, x, y);
        y += lineHeight;

        // 精度
        string accStr = "精度: ±" + ofToString(accuracy, 2) + " 米";
        chineseFont.drawString(accStr, x, y);
        y += lineHeight;

        // 坐标系统信息
        y += lineHeight * 0.5;
        ofSetColor(200, 200, 200);
        chineseFont.drawString("坐标系统: WGS84", x, y);
        y += lineHeight * 0.8;

        // 格式化的坐标显示
        string coordStr = "坐标: " + ofToString(latitude, 6) + ", " + ofToString(longitude, 6);
        chineseFont.drawString(coordStr, x, y);

    } else {
        ofSetColor(255, 200, 100);
        chineseFont.drawString("等待GPS信号...", x, y);
    }

    // 路径信息
    if(enablePathDrawing) {
        y += lineHeight * 0.5;
        ofSetColor(0, 255, 100);
        string pathInfo = "路径点数: " + ofToString(gpsPath.size());
        chineseFont.drawString(pathInfo, x, y);
        y += lineHeight * 0.8;

        ofSetColor(200, 200, 200);
        chineseFont.drawString("路径绘制: 开启", x, y);
    } else {
        y += lineHeight * 0.5;
        ofSetColor(255, 100, 100);
        chineseFont.drawString("路径绘制: 关闭", x, y);
    }

    // 底部提示信息
    y = ofGetHeight() - 150;
    ofSetColor(150, 150, 150);
    chineseFont.drawString("请确保已允许应用访问位置信息", x, y);
    y += 35;
    chineseFont.drawString("首次定位可能需要几分钟时间", x, y);
    y += 35;
    chineseFont.drawString("短按切换路径显示，长按清除路径", x, y);
}

#ifdef TARGET_OF_IOS
//--------------------------------------------------------------
void ofApp::touchDown(ofTouchEventArgs & touch){
    // 触摸屏幕刷新GPS
    if(gpsEnabled) {
        locationStatus = "正在刷新位置...";
        ofLogNotice("GPS") << "用户触摸刷新GPS";
    }

    // 记录触摸开始时间，用于长按检测
    touchStartTime = ofGetElapsedTimeMillis();
}

//--------------------------------------------------------------
void ofApp::touchMoved(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::touchUp(ofTouchEventArgs & touch){
    // 计算触摸持续时间
    unsigned long long touchDuration = ofGetElapsedTimeMillis() - touchStartTime;

    if(touchDuration > 1000) {  // 长按超过1秒
        // 长按清除GPS路径
        clearGpsPath();
        locationStatus = "GPS路径已清除";
        ofLogNotice("GPS Path") << "用户长按清除路径";
    } else if(touchDuration > 100) {  // 短按
        // 短按切换路径显示
        enablePathDrawing = !enablePathDrawing;
        locationStatus = enablePathDrawing ? "路径绘制已开启" : "路径绘制已关闭";
        ofLogNotice("GPS Path") << "路径绘制状态: " << (enablePathDrawing ? "开启" : "关闭");
    }
}

//--------------------------------------------------------------
void ofApp::touchDoubleTap(ofTouchEventArgs & touch){
    // 双击重启GPS服务
    if(gpsEnabled) {
        ofxGPS::stopLocation();
        if(ofxGPS::startLocation()) {
            locationStatus = "GPS服务已重启";
            ofLogNotice("GPS") << "GPS服务重启成功";
        } else {
            locationStatus = "GPS重启失败";
            ofLogError("GPS") << "GPS服务重启失败";
        }
    }
}

//--------------------------------------------------------------
void ofApp::touchCancelled(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::touchCancelled(ofTouchEventArgs & touch){

}

//--------------------------------------------------------------
void ofApp::lostFocus(){

}

//--------------------------------------------------------------
void ofApp::gotFocus(){

}

//--------------------------------------------------------------
void ofApp::gotMemoryWarning(){

}

//--------------------------------------------------------------
void ofApp::deviceOrientationChanged(int newOrientation){

}

//--------------------------------------------------------------
void ofApp::launchedWithURL(std::string url){

}

#else
// Desktop版本的事件处理
//--------------------------------------------------------------
void ofApp::keyPressed(int key){
    if(key == ' ') {
        // 空格键刷新GPS
        if(gpsEnabled) {
            locationStatus = "正在刷新位置...";
            ofLogNotice("GPS") << "用户按键刷新GPS";
        }
    }
}

//--------------------------------------------------------------
void ofApp::keyReleased(int key){

}

//--------------------------------------------------------------
void ofApp::mouseMoved(int x, int y){

}

//--------------------------------------------------------------
void ofApp::mouseDragged(int x, int y, int button){

}

//--------------------------------------------------------------
void ofApp::mousePressed(int x, int y, int button){
    // 鼠标点击刷新GPS
    if(gpsEnabled) {
        locationStatus = "正在刷新位置...";
        ofLogNotice("GPS") << "用户点击刷新GPS";
    }
}

//--------------------------------------------------------------
void ofApp::mouseReleased(int x, int y, int button){

}

//--------------------------------------------------------------
void ofApp::mouseEntered(int x, int y){

}

//--------------------------------------------------------------
void ofApp::mouseExited(int x, int y){

}

//--------------------------------------------------------------
void ofApp::windowResized(int w, int h){

}

//--------------------------------------------------------------
void ofApp::gotMessage(ofMessage msg){

}

//--------------------------------------------------------------
void ofApp::dragEvent(ofDragInfo dragInfo){

}
#endif

//--------------------------------------------------------------
void ofApp::onLocationData(const ofxGPS::LocationData & locationData){
    if(locationData.hasLocation) {
        latitude = locationData.latitude;
        longitude = locationData.longitude;
        accuracy = locationData.locationAccuracy;

        if(locationData.hasAltitude) {
            altitude = locationData.altitude;
        }

        locationStatus = "定位成功";
        ofLogNotice("GPS") << "位置更新: " << latitude << ", " << longitude << " 精度: " << accuracy;

        // 添加GPS点到路径（只有在精度足够好的情况下）
        if(accuracy < 50.0f && enablePathDrawing) {  // 精度小于50米才记录
            addGpsPoint(latitude, longitude);
        }
    } else {
        locationStatus = "定位失败";
        ofLogWarning("GPS") << "GPS定位数据无效";
    }
}

//--------------------------------------------------------------
void ofApp::drawGpsPath(){
    if(gpsPath.size() < 2) return;  // 至少需要2个点才能绘制路径

    // 使用VBO绘制路径线条
    if(pathVertices.size() > 1) {
        ofPushStyle();
        ofSetLineWidth(pathLineWidth);
        ofSetColor(pathColor);

        // 启用混合模式以支持透明度
        ofEnableBlendMode(OF_BLENDMODE_ALPHA);

        // 绘制路径线条
        pathVbo.draw(GL_LINE_STRIP, 0, pathVertices.size());

        // 绘制路径点
        ofSetColor(pathColor.r, pathColor.g, pathColor.b, 255);
        ofFill();
        for(const auto& point : gpsPath) {
            ofDrawCircle(point.x, point.y, 2);
        }

        // 如果有当前位置，绘制当前位置标记
        if(latitude != 0.0 && longitude != 0.0) {
            ofVec2f currentPos = convertGpsToScreen(latitude, longitude);
            ofSetColor(255, 0, 0, 200);  // 红色当前位置
            ofDrawCircle(currentPos.x, currentPos.y, 8);
            ofSetColor(255, 255, 255);
            ofDrawCircle(currentPos.x, currentPos.y, 4);
        }

        ofPopStyle();
    }
}

//--------------------------------------------------------------
ofVec2f ofApp::convertGpsToScreen(double lat, double lon){
    if(!hasInitialPosition) {
        return ofVec2f(ofGetWidth()/2, ofGetHeight()/2);
    }

    // 将GPS坐标转换为相对于地图中心的偏移量（米）
    double deltaLat = lat - mapCenter.x;
    double deltaLon = lon - mapCenter.y;

    // 转换为米（粗略计算）
    double metersPerDegreeLat = 111320.0;  // 纬度1度约等于111320米
    double metersPerDegreeLon = 111320.0 * cos(lat * M_PI / 180.0);  // 经度1度的米数随纬度变化

    double offsetX = deltaLon * metersPerDegreeLon;
    double offsetY = deltaLat * metersPerDegreeLat;

    // 转换为屏幕坐标
    float screenX = ofGetWidth()/2 + (offsetX * mapScale / 1000.0f);
    float screenY = ofGetHeight()/2 - (offsetY * mapScale / 1000.0f);  // Y轴翻转

    return ofVec2f(screenX, screenY);
}

//--------------------------------------------------------------
void ofApp::updatePathVbo(){
    if(gpsPath.empty()) return;

    pathVertices.clear();
    pathColors.clear();

    for(const auto& point : gpsPath) {
        pathVertices.push_back(ofVec3f(point.x, point.y, 0));
        pathColors.push_back(ofFloatColor(pathColor.r/255.0f, pathColor.g/255.0f, pathColor.b/255.0f, pathColor.a/255.0f));
    }

    // 更新VBO数据
    pathVbo.setVertexData(&pathVertices[0], pathVertices.size(), GL_DYNAMIC_DRAW);
    pathVbo.setColorData(&pathColors[0], pathColors.size(), GL_DYNAMIC_DRAW);
}

//--------------------------------------------------------------
void ofApp::addGpsPoint(double lat, double lon){
    ofVec2f screenPos = convertGpsToScreen(lat, lon);

    // 如果这是第一个GPS点，设置地图中心
    if(!hasInitialPosition) {
        mapCenter.x = lat;
        mapCenter.y = lon;
        hasInitialPosition = true;
        screenPos = ofVec2f(ofGetWidth()/2, ofGetHeight()/2);
    }

    // 检查距离阈值，避免GPS抖动
    if(gpsPath.size() > 0) {
        float distance = calculateDistance(lastGpsPoint, screenPos);
        if(distance < minDistanceThreshold) {
            return;  // 距离太近，不添加点
        }
    }

    // 添加新点
    gpsPath.push_back(screenPos);
    lastGpsPoint = screenPos;

    // 限制路径点数量，避免内存过度使用
    if(gpsPath.size() > 1000) {
        gpsPath.erase(gpsPath.begin());
    }

    // 更新VBO
    updatePathVbo();

    ofLogNotice("GPS Path") << "添加GPS点: " << lat << ", " << lon << " -> " << screenPos.x << ", " << screenPos.y;
}

//--------------------------------------------------------------
void ofApp::clearGpsPath(){
    gpsPath.clear();
    pathVertices.clear();
    pathColors.clear();
    hasInitialPosition = false;
    lastGpsPoint = ofVec2f(0, 0);
    mapCenter = ofVec2f(0, 0);

    // 清空VBO
    pathVbo.clear();

    ofLogNotice("GPS Path") << "GPS路径已清除";
}

//--------------------------------------------------------------
float ofApp::calculateDistance(ofVec2f p1, ofVec2f p2){
    return sqrt((p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y));
}


