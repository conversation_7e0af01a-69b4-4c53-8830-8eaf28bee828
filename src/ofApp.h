#pragma once

#include "ofMain.h"
#include "ofxGPS.h"
#include "ofxFontStash.h"

#ifdef TARGET_OF_IOS
#include "ofxiOS.h"
class ofApp : public ofxiOSApp {
#else
class ofApp : public ofBaseApp {
#endif

    public:
        void setup() override;
        void update() override;
        void draw() override;
        void exit() override;

#ifdef TARGET_OF_IOS
        void touchDown(ofTouchEventArgs & touch) override;
        void touchMoved(ofTouchEventArgs & touch) override;
        void touchUp(ofTouchEventArgs & touch) override;
        void touchDoubleTap(ofTouchEventArgs & touch) override;
        void touchCancelled(ofTouchEventArgs & touch) override;

        void lostFocus() override;
        void gotFocus() override;
        void gotMemoryWarning() override;
        void deviceOrientationChanged(int newOrientation) override;
        void launchedWithURL(std::string url) override;
#else
        void keyPressed(int key) override;
        void keyReleased(int key) override;
        void mouseMoved(int x, int y) override;
        void mouseDragged(int x, int y, int button) override;
        void mousePressed(int x, int y, int button) override;
        void mouseReleased(int x, int y, int button) override;
        void mouseEntered(int x, int y) override;
        void mouseExited(int x, int y) override;
        void windowResized(int w, int h) override;
        void dragEvent(ofDragInfo dragInfo) override;
        void gotMessage(ofMessage msg) override;
#endif

        // GPS相关
        bool gpsEnabled;

        // 字体相关
        ofxFontStash chineseFont;

        // 位置信息
        double latitude;
        double longitude;
        double altitude;
        double accuracy;
        string locationStatus;

        // GPS路径绘制相关
        vector<ofVec2f> gpsPath;           // 存储GPS路径点
        ofPath pathLine;                   // 路径绘制对象
        ofVbo pathVbo;                     // VBO用于高效渲染
        vector<ofVec3f> pathVertices;      // 路径顶点数据
        vector<ofFloatColor> pathColors;   // 路径颜色数据
        bool enablePathDrawing;            // 是否启用路径绘制
        float pathLineWidth;               // 路径线条宽度
        ofColor pathColor;                 // 路径颜色
        ofVec2f lastGpsPoint;             // 上一个GPS点
        float minDistanceThreshold;        // 最小距离阈值
        ofVec2f mapCenter;                // 地图中心点（GPS坐标）
        float mapScale;                   // 地图缩放比例
        bool hasInitialPosition;          // 是否已有初始位置
        unsigned long long touchStartTime; // 触摸开始时间

        // GPS事件处理
        void onLocationData(const ofxGPS::LocationData & locationData);

        // UI相关
        void drawLocationInfo();
        void drawGpsPath();

        // 路径处理相关
        ofVec2f convertGpsToScreen(double lat, double lon);
        void updatePathVbo();
        void addGpsPoint(double lat, double lon);
        void clearGpsPath();
        float calculateDistance(ofVec2f p1, ofVec2f p2);

};
